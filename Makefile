# Variables
PROJECT_NAME = robot-world
VERSION = 0.0.1-SNAPSHOT

# Targets
.PHONY: all compile test reference-server own-server clean package release tag stop full-run-ref full_run_own

all: compile test

compile:
	@bash "./scripts/compile.sh"

test: reference-server
	@bash "./scripts/test.sh"

reference-server:
	@bash "./scripts/reference-server.sh"

own-server:
	@bash "./scripts/own-server.sh"

package:
	@bash "./scripts/package.sh"

release: clean
	@bash "./scripts/release.sh"

clean:
	@bash "./scripts/clean.sh"

tag:
	@bash "./scripts/tag.sh"

stop:
	@bash "./scripts/stop.sh"

full_run_ref:
	@bash "./scripts/full_run_reference_server.sh"

full_run_own:
	@bash "./scripts/full_run_own_server.sh"